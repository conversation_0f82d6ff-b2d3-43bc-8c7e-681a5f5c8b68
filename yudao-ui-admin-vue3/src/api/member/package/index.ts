/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-06-16 14:48:06
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-06-17 21:49:50
 * @FilePath: /yudao/yudao-ui-admin-vue3/src/api/member/package/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/config/axios'

export interface MemberPackageConfigVO {
  id: number
  name: string
  price: number
  voucherCount: number
  voucherTemplateId: number
  sort: number
  status: number
  remark?: string
  createTime?: Date
}

// 查询会员套餐配置分页
export const getPackageConfigPage = async (params: any) => {
  return await request.get({ url: `/member/package-config/page`, params })
}

// 查询会员套餐配置详情
export const getPackageConfig = async (id: number) => {
  return await request.get({ url: `/member/package-config/get?id=` + id })
}

// 查询启用状态的会员套餐配置列表
export const getSimplePackageConfigList = async () => {
  return await request.get({ url: `/member/package-config/simple-list` })
}

// 新增会员套餐配置
export const createPackageConfig = async (data: MemberPackageConfigVO) => {
  return await request.post({ url: `/member/package-config/create`, data })
}

// 修改会员套餐配置
export const updatePackageConfig = async (data: MemberPackageConfigVO) => {
  return await request.put({ url: `/member/package-config/update`, data })
}

// 删除会员套餐配置
export const deletePackageConfig = async (id: number) => {
  return await request.delete({ url: `/member/package-config/delete?id=` + id })
} 