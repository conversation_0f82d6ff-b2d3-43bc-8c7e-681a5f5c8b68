<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2025-06-01 20:09:10
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-06-13 10:42:13
 * @FilePath: /education-platform/Users/<USER>/Documents/cursor/yudao/ruoyi-vue-pro/script/docker/README-dev.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# RuoYi-Vue-Pro 开发环境 Docker 配置

## 目录结构
```
script/docker/
├── docker-compose.yml       # 生产环境 Docker Compose配置文件
├── docker-compose-dev.yml   # 开发环境 Docker Compose配置文件（仅MySQL和Redis）
├── docker.env               # 生产环境环境变量文件
├── docker.env-dev           # 开发环境环境变量文件
└── Docker-HOWTO.md          # Docker使用说明
```

## 开发环境使用指南

### 1. 启动开发环境基础服务(MySQL和Redis)
```bash
cd ruoyi-vue-pro/script/docker
docker compose -f docker-compose-dev.yml --env-file docker.env-dev up -d
```

### 2. 查看容器状态
```bash
docker-compose -f docker-compose-dev.yml ps
```

### 3. 初始化数据库
Docker会自动挂载SQL脚本并初始化数据库，如果需要手动导入其他脚本：
```bash
# 进入MySQL容器
docker exec -it yudao-mysql-dev bash

# 登录MySQL
mysql -u root -p
# 输入密码: 123456

# 退出MySQL和容器
exit
exit
```

### 4. 启动后端项目
在IDE中运行`yudao-server`模块的`YudaoServerApplication`类，或使用Maven命令：
```bash
cd ruoyi-vue-pro
mvn spring-boot:run -pl yudao-server -Dmaven.test.skip=true
```

### 5. 启动前端项目
```bash
cd ruoyi-vue-pro/yudao-ui-admin
npm install
npm run dev
```

### 6. 停止开发环境
```bash
cd ruoyi-vue-pro/script/docker
docker compose -f docker-compose-dev.yml down
```

## 注意事项
- 开发环境配置会自动使用`docker.env-dev`中的环境变量
- MySQL和Redis数据存储在Docker卷中，重启Docker不会丢失数据
- 如果需要完全重置环境，可以使用`docker-compose -f docker-compose-dev.yml down -v`删除卷 