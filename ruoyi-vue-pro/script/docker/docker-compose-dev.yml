version: "3.4"

name: yudao-system-dev

services:
  mysql:
    container_name: yudao-mysql-dev
    image: mysql:8
    restart: unless-stopped
    tty: true
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ruoyi-vue-pro}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
    volumes:
      - mysql:/var/lib/mysql/
      - ../../sql/mysql/ruoyi-vue-pro.sql:/docker-entrypoint-initdb.d/ruoyi-vue-pro.sql:ro

  redis:
    container_name: yudao-redis-dev
    image: redis:6-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis:/data

volumes:
  mysql:
    driver: local
  redis:
    driver: local 