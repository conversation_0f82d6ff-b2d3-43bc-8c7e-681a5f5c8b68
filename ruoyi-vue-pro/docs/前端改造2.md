<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2025-06-12 22:20:28
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-06-12 22:21:26
 * @FilePath: /yudao/ruoyi-vue-pro/docs/前端改造2.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

Read file: yudao/yudao-mall-uniapp/pages.json
让我根据已有的信息来分析您的需求：

根据您提供的信息和项目结构，我来详细回答您的三个问题：

## 1. 前端需要修改的地方

### yudao-ui-admin-vue3（后台管理系统）

**需要修改的模块：**

1. **mall/product/** - 商品管理 → 课程管理
   - 商品列表页面文案替换
   - 商品详情表单增加课程相关字段（总课时、试听课时、年龄段等）
   - 商品分类 → 课程分类

2. **mall/promotion/** - 营销管理 → 营销+试听券管理  
   - 优惠券模板增加试听券标识字段
   - 拼团活动管理

3. **mall/trade/** - 订单管理 → 课程订单管理
   - 订单列表显示课程相关信息

4. **新增 mall/institution/** - 机构管理（已创建）
   - 机构列表、详情、地理位置设置

5. **member/** - 会员管理
   - 会员等级设置（99元3张券，128元5张券等）
   - 会员地理位置信息

### yudao-mall-uniapp（移动端）

**需要修改的模块：**

1. **pages/goods/** → **pages/course/**
   - 商品详情页 → 课程详情页
   - 增加机构信息展示
   - 距离显示和地理位置

2. **pages/index/** - 首页
   - 附近机构展示
   - 课程分类展示
   - 地理位置切换功能

3. **pages/user/** - 个人中心  
   - 我的试听券
   - 分销中心
   - 会员等级展示

4. **新增功能：**
   - 地理位置服务集成
   - 机构筛选和距离计算
   - 试听券使用流程

## 2. 文案替换对照表

**是的，需要按照对照表进行替换**，具体包括：

- 商品 → 课程
- 立即购买 → 立即报名  
- 库存 → 名额
- 规格 → 课程安排
- 优惠券 → 试听券

这些文案替换是必要的，符合教育行业的用词习惯。

## 3. UniApp装修配置功能

**是的，UniApp前端支持后台装修配置**。从项目结构看：

1. **sheep框架支持组件化开发**，可以通过后台配置页面布局
2. **一期简化功能完全可行**：
   - 课程订单（会员兑换）
   - 机构展示  
   - 我的（包括分销）

3. **建议的一期页面结构：**
   ```
   底部导航：
   - 首页（附近机构+课程）
   - 机构（机构列表+地图）  
   - 订单（课程订单）
   - 我的（个人中心+分销）
   ```

## 实施建议

1. **优先级排序：**
   - 高优先级：文案替换、基础功能改造
   - 中优先级：地理位置功能、机构管理
   - 低优先级：装修配置、高级营销功能

2. **分阶段实施：**
   - 第一阶段：完成文案替换和基本功能改造
   - 第二阶段：实现地理位置和机构功能  
   - 第三阶段：完善试听券和会员体系

需要我详细分析某个具体模块的修改方案吗？