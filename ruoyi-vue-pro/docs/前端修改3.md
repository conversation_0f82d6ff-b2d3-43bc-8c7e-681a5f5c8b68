根据当前进度和您的需求，我制定一个详细的改造计划：

## 🎯 整体改造策略

### 阶段一：基础功能适配（当前阶段）✅
**目标：让现有功能适配教育行业**
- [x] 文案替换（已完成）
- [ ] 数据字段适配
- [ ] 基础业务逻辑调整

### 阶段二：核心教育功能开发
**目标：实现教育行业特有功能**
- [ ] 机构管理完善
- [ ] 试听券系统
- [ ] 会员分级购买

### 阶段三：地理位置和用户体验
**目标：提升用户体验和商业模式**
- [ ] 地理位置功能
- [ ] 用户界面优化
- [ ] 商户端功能

---

## 📋 详细实施计划

### **当前阶段：基础适配（预计3-5天）**

#### 1. **后台管理系统适配**
- **商品管理页面**：
  - 增加课程特有字段显示（总课时、试听课时、年龄段）
  - 修改表格列标题和筛选条件
  - 优化课程分类管理界面
  
- **机构管理完善**：
  - 完善机构列表页面的功能按钮
  - 增加机构地理位置设置界面
  - 添加机构状态管理功能

- **试听券管理**：
  - 在优惠券模板中增加试听券标识字段
  - 修改优惠券列表显示逻辑
  - 增加试听券专用的创建模板

#### 2. **移动端界面适配**
- **首页改造**：
  - 课程分类展示优化
  - 附近机构模块设计
  - 推荐课程展示逻辑

- **课程详情页**：
  - 增加机构信息展示区域
  - 显示课程时长、适合年龄等信息
  - 优化报名按钮和流程

- **个人中心**：
  - 我的试听券页面
  - 会员等级显示
  - 分销中心入口优化

### **第二阶段：核心功能开发（预计7-10天）**

#### 1. **会员分级购买系统**
- **会员套餐设计**：
  - 99元套餐：3张试听券
  - 128元套餐：5张试听券
  - 更高级套餐的扩展性设计

- **试听券核心逻辑**：
  - 券的生成和分发机制
  - 使用限制和验证逻辑
  - 与现有订单系统的整合

- **会员权益管理**：
  - 会员等级升级逻辑
  - 权益展示和使用记录
  - 积分体系与会员的关联

#### 2. **机构端功能完善**
- **机构入驻流程**：
  - 申请表单和审核流程
  - 机构信息完善指引
  - 资质上传和验证

- **课程管理**：
  - 机构发布课程功能
  - 课程排期和名额管理
  - 试听课程特殊设置

### **第三阶段：地理位置和体验优化（预计5-7天）**

#### 1. **地理位置功能**
- **定位服务**：
  - 用户当前位置获取
  - 地址切换功能（类似美团）
  - 位置权限管理

- **距离计算和展示**：
  - 机构距离计算算法
  - 距离排序和筛选
  - 地图展示和导航集成

- **附近机构功能**：
  - 基于位置的机构推荐
  - 地图模式和列表模式切换
  - 机构详情页地理信息

#### 2. **用户体验优化**
- **搜索和筛选**：
  - 课程搜索功能增强
  - 年龄段、距离、价格等筛选
  - 搜索结果优化排序

- **页面性能优化**：
  - 图片懒加载和压缩
  - 页面加载速度优化
  - 缓存策略优化

---

## 🛠 技术实施重点

### **前端改造重点**
1. **组件复用性**：基于现有组件进行扩展，而非重写
2. **数据流适配**：确保新字段在各组件间正确传递
3. **响应式设计**：考虑不同设备的展示效果

### **后端对接策略**
1. **API兼容性**：确保前端改造不破坏现有API调用
2. **数据验证**：新增字段的前端验证逻辑
3. **错误处理**：完善新功能的异常处理机制

### **测试验证方案**
1. **功能测试**：每个阶段完成后进行功能验证
2. **兼容性测试**：确保新功能不影响现有功能
3. **用户体验测试**：关键流程的用户体验验证

---

## 📅 时间节点规划

| 阶段 | 时间 | 主要产出 | 验收标准 |
|------|------|----------|----------|
| 阶段一 | 3-5天 | 基础适配完成 | 所有页面正确显示课程相关信息 |
| 阶段二 | 7-10天 | 核心功能上线 | 试听券购买使用流程完整 |
| 阶段三 | 5-7天 | 体验优化完成 | 地理位置功能正常，用户体验流畅 |

## 🎯 下一步行动

**立即开始：阶段一的后台管理适配**
1. 先完善机构管理页面的基础功能
2. 然后优化商品管理页面的课程字段显示
3. 最后处理试听券模板的界面调整

这个计划确保了循序渐进的开发，每个阶段都有明确的目标和产出，同时保持系统的稳定性。您觉得这个计划如何？需要调整哪个部分的优先级吗？