<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2025-06-12 17:45:58
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-06-12 22:11:00
 * @FilePath: /edb2/Users/<USER>/Documents/cursor/yudao/ruoyi-vue-pro/docs/文案替换对照表.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 儿童教育会员试学团购平台 - 文案替换对照表

## 前端文案替换

| 原文案 | 替换后文案 |
|-------|----------|
| 商品详情 | 课程详情 |
| 商品分类 | 课程分类 |
| 商品列表 | 课程列表 |
| 拼团商品 | 团购课程 |
| 秒杀商品 | 特惠课程 |
| 积分商品 | 积分课程 |
| 评价商品 | 评价课程 |
| 商品评价 | 课程评价 |
| 立即购买 | 立即报名 |
| 购买数量 | 报名数量 |
| 库存不足 | 名额已满 |
| 已售罄 | 名额已满 |
| 请选择规格 | 请选择课程安排 |
| 规格 | 课程安排 |
| 库存 | 名额 |

## 后台菜单替换

| 原菜单名称 | 替换后菜单名称 |
|----------|-------------|
| 商品管理 | 课程管理 |
| 商品分类 | 课程分类 |
| 优惠券管理 | 试听券管理 |
| 订单管理 | 课程订单 |
| 拼团商品 | 团购课程 |

## 数据库表扩展

1. 新增表：`edu_institution` - 教育机构表
   - 包含机构基本信息、地理位置、联系方式等

2. 扩展表：`product_spu` - 商品表扩展为课程
   - 新增字段：
     - `institution_id` - 所属机构ID
     - `total_sessions` - 总课时数
     - `trial_sessions` - 试听课时数
     - `age_range` - 适合年龄段
     - `course_start_time` - 课程开始时间
     - `course_end_time` - 课程结束时间
     - `is_trial` - 是否为试听课程

3. 扩展表：`promotion_coupon_template` - 优惠券表扩展为试听券
   - 新增字段：
     - `is_trial_voucher` - 是否为试听券

4. 扩展表：`trade_order` - 订单表扩展为课程订单
   - 新增字段：
     - `is_course_order` - 是否为课程订单

## 已完成的修改

1. 修改了 `yudao-mall-uniapp/pages.json` 中的页面标题
2. 修改了 `yudao-mall-uniapp/sheep/components/s-select-sku/s-select-sku.vue` 中的购买相关文案
3. 修改了 `yudao-mall-uniapp/pages/goods/index.vue` 中的商品详情页文案
4. 创建了 `ruoyi-vue-pro/sql/mysql/09-education-institution.sql` 教育机构表结构
5. 创建了 `ruoyi-vue-pro/sql/mysql/10-education-product-extend.sql` 商品表扩展
6. 创建了 `yudao-ui-admin-vue3/src/views/mall/institution/index.vue` 机构管理页面 