# 少儿教育平台数据库设计变更说明

## 核心设计原则
**最小改动，最大复用** - 充分利用现有电商系统，只新增必要的教育功能

## 表结构变更总结

### 新增表（4个教育核心表）
1. **education_institution_category** - 机构分类表
2. **education_institution_apply** - 机构申请表（含位置信息）
3. **education_institution** - 机构信息表（含位置信息）
4. **education_trial_usage** - 试听课程使用记录表

### 新增表（1个位置功能表）
5. **member_location_preference** - 用户位置偏好表（周边查看功能）

### 修改现有表（2个扩展字段）

#### member_level表新增字段：
- `trial_institution_count` - 可试听机构数量
- `trial_validity_days` - 试听权益有效期(天)

#### member_user表新增字段：
- `trial_used_institutions` - 已使用试听机构数
- `trial_expire_time` - 试听权益过期时间

### 已存在的字段（无需新增）
- `member_user.point` - 积分字段（已存在）
- `member_user.level_id` - 会员等级编号（已存在）
- `member_user.experience` - 会员经验（已存在）

## 业务逻辑设计

### 试听权益系统

#### 核心逻辑
```
用户充值 → 获得会员等级 → 享受对应试听权益
```

#### 等级权益示例
| 充值金额 | 会员等级 | 试听机构数 | 有效期 |
|----------|----------|------------|--------|
| 99元 | 铜牌会员 | 3个机构 | 365天 |
| 128元 | 银牌会员 | 5个机构 | 365天 |
| 199元 | 金牌会员 | 8个机构 | 365天 |

#### 业务流程
1. 用户充值获得会员等级
2. 根据会员等级获得试听权益（机构数量、有效期）
3. 用户在有效期内可以试听指定数量的机构课程
4. 每试听一个新机构，`trial_used_institutions`计数+1
5. 当达到等级限制或过期时，无法继续试听

### 位置服务设计

#### 收货地址 vs 位置偏好
- **member_address**: 实体商品收货地址（保持原有功能）
- **member_location_preference**: 周边查看位置偏好（新增功能）

#### 使用场景
- 用户设置位置偏好 → 系统显示周边机构
- 类似美团/饿了么的地图查看周边商家
- 支持多个偏好位置（家、公司等）

## 复用策略

### 课程管理
- **课程信息** → 复用 `product_spu`（商品表）
- **课程分类** → 复用 `product_category`（商品分类）
- **课程评价** → 复用 `product_comment`（商品评价）

### 订单系统
- **课程下单** → 复用 `trade_order`/`trade_order_item`
- **支付系统** → 复用整套支付模块

### 会员系统
- **学员管理** → 复用 `member_user`（会员表）
- **积分系统** → 复用 `member_point_*`（积分体系）
- **等级系统** → 扩展 `member_level`（会员等级）

### 营销功能
- **拼团活动** → 复用 `promotion_combination_*`
- **分销返佣** → 复用 `brokerage_*`
- **优惠券** → 复用 `promotion_coupon_*`

## 开发指导

### 后端开发
1. **机构管理模块**：实现机构入驻、审核、信息管理
2. **试听权益模块**：基于会员等级的试听权益管理
3. **位置服务模块**：周边机构查询、距离计算
4. **课程管理模块**：复用商品系统，扩展教育属性

### 前端开发
1. **机构端**：机构入驻申请、信息管理、课程发布
2. **用户端**：周边机构查看、课程试听、会员权益
3. **管理端**：机构审核、试听权益配置、数据统计

### 开发优先级
1. **P0-核心功能**：机构管理、试听权益、课程复用
2. **P1-重要功能**：位置服务、营销工具
3. **P2-增强功能**：数据统计、高级营销

## 技术要点

### 地理位置计算
```sql
-- 计算两点间距离的SQL函数
SELECT (
  6371 * acos(
    cos(radians(?)) * cos(radians(latitude)) * 
    cos(radians(longitude) - radians(?)) + 
    sin(radians(?)) * sin(radians(latitude))
  )
) AS distance
```

### 试听权益判断
```java
// 检查用户试听权益
public boolean checkTrialEligibility(Long userId, Long institutionId) {
    MemberUser user = memberUserMapper.selectById(userId);
    MemberLevel level = memberLevelMapper.selectById(user.getLevelId());
    
    // 检查是否过期
    if (user.getTrialExpireTime().isBefore(LocalDateTime.now())) {
        return false;
    }
    
    // 检查机构数量限制
    if (user.getTrialUsedInstitutions() >= level.getTrialInstitutionCount()) {
        return false;
    }
    
    // 检查是否已在该机构试听过
    return !educationTrialUsageMapper.existsByUserIdAndInstitutionId(userId, institutionId);
}
```

### 周边查询优化
```java
// 使用空间索引优化周边查询
@Select("SELECT *, " +
        "ST_Distance_Sphere(point(longitude, latitude), point(#{longitude}, #{latitude})) as distance " +
        "FROM education_institution " +
        "WHERE ST_Distance_Sphere(point(longitude, latitude), point(#{longitude}, #{latitude})) <= #{radius} " +
        "ORDER BY distance LIMIT #{limit}")
List<InstitutionVO> findNearbyInstitutions(@Param("longitude") BigDecimal longitude, 
                                          @Param("latitude") BigDecimal latitude, 
                                          @Param("radius") Integer radius, 
                                          @Param("limit") Integer limit);
```

## 数据迁移建议

### 现有用户处理
1. 为现有用户设置默认会员等级
2. 初始化试听权益（可选择性给予体验权益）
3. 引导用户设置位置偏好

### 商品数据处理
1. 现有商品可标记为课程类型
2. 增加教育相关属性（机构、适龄、课时等）
3. 调整分类结构适配教育场景

## 总结

通过最小化改动，成功将电商平台改造为少儿教育平台：
- ✅ 仅新增5个表（4个教育核心+1个位置）
- ✅ 仅扩展2个表的4个字段
- ✅ 充分复用现有90%以上的功能
- ✅ 保持系统架构清晰，开发成本最低