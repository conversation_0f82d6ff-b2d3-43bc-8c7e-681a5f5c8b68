您说得对！让我重新分析一下，确认分销系统的支持情况，然后重点规划多商户和地理位置功能的实现方案。
Ran tool
这些搜索结果显示的是其他项目的分销功能，让我专门查看ruoyi-vue-pro中的分销支持情况：
Ran tool
好的，基于前面发现的`brokerageEnabled`字段，可以确认ruoyi-vue-pro确实有分销基础。现在我来制定详细的实施方案，重点解决**多商户**和**地理位置**两个核心问题：

## 🎯 核心实施方案

### 📊 现状分析
- ✅ **已支持**：会员系统、拼团、营销、支付、商城、分销基础
- ❌ **缺失**：多商户系统、地理位置服务
- 🔄 **需适配**：课程试听业务逻辑

### 🏗️ 详细实施计划

## 一、多商户系统实现（核心难点）

### 1.1 数据库设计改造
**基于现有的多租户改造为多商户**
- 扩展`system_tenant`表为商户表，增加：商户类型、经营范围、地址、联系人等
- 新建商户申请表：入驻申请、审核流程、资质材料

### 1.2 商户入驻流程
**设计完整的入驻审核机制**
- 前端：商户注册页面，填写基本信息、上传资质
- 后台：管理员审核界面，支持通过/拒绝/要求补充材料
- 通知：短信/邮件通知审核结果
- 权限：审核通过后自动开通商户后台权限

### 1.3 商户后台适配
**基于现有的admin后台定制商户版本**
- 数据隔离：商户只能看到自己的商品、订单、会员数据
- 功能限制：根据商户套餐限制上传商品数量、使用的营销工具等

## 二、地理位置服务实现

### 2.1 地图服务集成
**选用高德地图API（国内使用率高，文档完善）**
- 申请高德开发者账号，获取API Key
- 集成地理编码服务：地址转经纬度
- 集成逆地理编码：经纬度转详细地址
- 集成距离计算API：计算两点直线距离

### 2.2 数据库设计
**新建地理位置相关表**
- 商户位置表：存储商户经纬度、详细地址、营业时间
- 用户位置表：存储用户当前位置（可选，隐私考虑）
- 服务范围表：定义商户服务半径

### 2.3 前端功能实现
**用户端位置相关功能**
- 获取用户当前位置（需要授权）
- 显示附近商户列表，按距离排序
- 商户详情页显示地图位置
- 支持切换查看区域（类似美团外卖）

## 三、业务流程适配

### 3.1 课程试听券系统
**基于现有优惠券系统改造**
- 优惠券类型新增：试听券
- 试听券规则：指定课程、指定次数、使用限制
- 会员套餐：购买后自动发放对应数量的试听券
- 核销机制：商户扫码或后台核销

### 3.2 订单流程优化
**适配教育行业特点**
- 订单类型：会员套餐购买、试听预约
- 预约系统：选择时间段、选择课程
- 核销记录：记录试听券使用情况
- 评价系统：试听后的评价反馈

## 🛠️ 具体实施步骤

### 第一阶段：多商户基础架构（2-3周）
1. **数据库表设计和创建**
   - 商户信息表、商户申请表、商户等级表
   - 修改现有商品、订单表，增加商户ID关联

2. **后台管理功能**
   - 商户申请审核页面
   - 商户信息管理页面
   - 商户等级配置页面

3. **权限系统改造**
   - 新增商户角色类型
   - 菜单权限按商户类型过滤
   - 数据权限按商户ID隔离

### 第二阶段：地理位置服务（1-2周）
1. **地图API集成**
   - 申请高德地图API
   - 封装地图服务工具类
   - 实现地址解析和距离计算

2. **位置管理功能**
   - 商户位置设置页面
   - 前端地图选点功能
   - 用户端位置获取和附近商户展示

### 第三阶段：业务逻辑适配（1-2周）
1. **试听券系统**
   - 基于优惠券模块扩展试听券类型
   - 会员套餐购买后自动发券逻辑
   - 试听券核销功能

2. **前端页面开发**
   - 商户列表页（按距离排序）
   - 课程详情页（显示试听设置）
   - 会员套餐购买页面

### 第四阶段：测试和优化（1周）
1. **功能测试**
   - 多商户数据隔离测试
   - 地理位置功能测试
   - 试听券流程测试

2. **性能优化**
   - 地理位置查询优化（使用空间索引）
   - 附近商户查询缓存优化

## 🔧 技术选型和注意事项

### 技术选型
- **地图服务**：高德地图（国内稳定性好）
- **距离计算**：使用高德距离计算API + 本地缓存
- **数据库**：在现有MySQL基础上新增空间索引
- **缓存策略**：Redis缓存附近商户查询结果

### 关键注意事项
1. **数据隔离**：确保商户间数据严格隔离，防止数据泄露
2. **性能考虑**：地理位置查询要做好缓存，避免频繁调用API
3. **隐私保护**：用户位置信息要做脱敏处理，提供位置开关
4. **扩展性**：设计时考虑未来可能的多城市、多区域扩展

这样的方案既充分利用了ruoyi-vue-pro的现有功能，又专门解决了您提到的多商户和地理位置两个核心问题。整个开发周期预计5-8周，您觉得这个方案如何？