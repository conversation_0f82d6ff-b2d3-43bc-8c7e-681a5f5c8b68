Ran tool
Ran tool
Ran tool
Ran tool
Ran tool
让我更精确地搜索mer_java中的多商户相关功能：
Ran tool
Read file: other/JAVA-MER-1.8.1_20250314/mer_java/crmeb-service/src/main/java/com/zbkj/service/service/MerchantApplyService.java
Ran tool
让我基于现有信息来分析若依项目中缺失的字段和概念：

## 📊 字段对比分析

### 🔍 若依现有的相关功能
Ran tool
Ran tool
现在让我检查商户入驻相关的字段：
Ran tool
Ran tool
基于分析，让我总结一下：

## 📋 确定的字段对比分析

### ✅ 若依**已有**的功能和字段

1. **地理位置功能** - **完全存在**
   - `latitude` (纬度) 和 `longitude` (经度) 字段
   - 距离计算功能 `NumberUtils.getDistance()`
   - 自提门店管理 (`DeliveryPickUpStoreDO`)

2. **多租户基础** - **完全存在**
   - 租户管理系统
   - 租户隔离机制
   - 权限管理

### 🔍 多租户 vs 多商户的本质区别
Ran tool
Ran tool
Ran tool
让我用简单的图表格式：
Ran tool
## 📊 **确认需要新增的字段（原项目确实没有）**

### 🆕 **教育行业特有字段**

```sql
-- 1. 机构资质字段（确实需要新增）
business_license VARCHAR(500) COMMENT '营业执照URL',
education_license VARCHAR(500) COMMENT '办学许可证URL', 
teacher_count INTEGER COMMENT '师资数量',

-- 2. 课程试听相关（确实需要新增）
trial_course_limit INTEGER COMMENT '试听课程数量限制',
trial_course_used INTEGER COMMENT '已用试听次数',

-- 3. 会员99元体验卡（确实需要新增）
membership_trial_institutions INTEGER COMMENT '可选机构数量',
```

### ✅ **建议方案：混合模式**

**最佳实践**：
1. **保留若依多租户** - 用于真正需要数据隔离的场景
2. **增加多商户概念** - 用于教育机构管理

```sql
-- 新增机构表（基于若依现有门店表扩展）
CREATE TABLE education_institution (
  id BIGINT PRIMARY KEY,
  tenant_id BIGINT COMMENT '关联租户ID（可选）',
  name VARCHAR(100) NOT NULL COMMENT '机构名称',
  
  -- 复用若依现有字段
  latitude DOUBLE COMMENT '纬度',
  longitude DOUBLE COMMENT '经度', 
  address VARCHAR(500) COMMENT '详细地址',
  
  -- 新增教育特有字段
  business_license VARCHAR(500) COMMENT '营业执照',
  education_license VARCHAR(500) COMMENT '办学许可证',
  teacher_count INTEGER DEFAULT 0 COMMENT '师资数量',
  
  -- 复用若依状态管理
  status TINYINT DEFAULT 1 COMMENT '状态：0停用 1启用',
  audit_status TINYINT DEFAULT 0 COMMENT '审核状态：0待审核 1通过 2拒绝'
);
```

## 🎯 **最小改动方案总结**

### ✅ **不需要改动的（直接复用）**
- 多租户基础架构
- 地理位置功能
- 支付系统
- 会员积分系统  
- 分销系统
- 营销系统

### 🔧 **只需要文案改动**
- "商品管理" → "课程管理"
- "门店管理" → "机构管理"
- "租户管理" → "平台管理"

### 🆕 **确实需要新增的**
- 教育资质字段（3-4个字段）
- 试听课程逻辑（2-3个字段）
- 99元体验卡逻辑（2-3个表）

**风险评估**：新增字段和表的风险很低，不会影响现有功能。