# 少儿教育平台数据库结构重新整理

## 项目概述

本项目将电商平台改造为少儿教育平台，支持线下课程报名、线上下单、课程分类、积分体系、分销（二级分佣）、会员等级、拼团、99元会员试听卡、营销、多商户、商户入驻、用户地址切换、距离显示等功能。

## 数据库设计原则

1. **最小改动原则**：保持原有表名和字段属性不变，主要改动显示文案
2. **功能模块化**：按功能将表结构分类整理
3. **去重优化**：移除重复表，统计唯一表数量
4. **教育特色**：结合少儿教育平台特点调整表注释

## SQL文件组织结构

### 01-system-base.sql - 系统基础模块
- **用户管理**：`system_users`、`system_user_role`
- **角色权限**：`system_role`、`system_menu`、`system_role_menu`
- **组织架构**：`system_dept`、`system_post`
- **字典管理**：`system_dict_type`、`system_dict_data`
- **参数配置**：`infra_config`
- **租户管理**：`system_tenant`、`system_tenant_package`

### 02-education-core.sql - 教育核心模块
- **机构管理**：`education_institution_category`、`education_institution_apply`、`education_institution`
- **课程管理**：`education_course_category`、`education_course`
- **教师管理**：`education_teacher`、`education_course_teacher`
- **班级管理**：`education_class`、`education_class_student`
- **试听卡**：`education_user_trial_card`、`education_trial_card_usage`

### 03-mall-order.sql - 商城订单模块
- **购物车**：`trade_cart`
- **订单管理**：`trade_order`、`trade_order_item`
- **售后管理**：`trade_after_sale`
- **物流配送**：`trade_delivery_express`、`trade_delivery_template`、`trade_delivery_template_charge`、`trade_delivery_template_free`
- **收货地址**：`member_address`
- **地区管理**：`system_area`

### 04-marketing.sql - 营销功能模块
- **优惠券**：`promotion_coupon_template`、`promotion_coupon`
- **积分管理**：`member_sign_in_config`、`member_sign_in_record`、`member_point_record`、`member_point_config`
- **分销管理**：`brokerage_user`、`brokerage_record`、`brokerage_withdraw`
- **拼团活动**：`promotion_combination_activity`、`promotion_combination_record`
- **Banner管理**：`promotion_banner`
- **秒杀活动**：`promotion_seckill_activity`、`promotion_seckill_config`、`promotion_seckill_product`

### 05-member.sql - 会员体系模块
- **会员等级**：`member_level`、`member_level_record`
- **用户信息**：`member_user`、`member_tag`、`member_statistics`、`member_experience_record`
- **学员管理**：`education_student`、`education_student_learning_record`
- **评价管理**：`education_course_comment`

### 06-infrastructure.sql - 基础设施模块
- **文件管理**：`infra_file`、`infra_file_config`
- **日志管理**：`infra_api_access_log`、`infra_api_error_log`、`system_operate_log`、`system_login_log`
- **短信管理**：`system_sms_channel`、`system_sms_template`、`system_sms_code`、`system_sms_log`
- **邮件管理**：`system_mail_account`、`system_mail_template`、`system_mail_log`
- **站内信**：`system_notify_template`、`system_notify_message`
- **代码生成**：`infra_codegen_table`、`infra_codegen_column`、`infra_data_source_config`

### 07-payment.sql - 支付模块
- **支付应用**：`pay_app`、`pay_channel`
- **支付订单**：`pay_order`、`pay_order_extension`
- **退款管理**：`pay_refund`
- **支付通知**：`pay_notify_task`、`pay_notify_log`
- **转账管理**：`pay_transfer`
- **钱包管理**：`pay_wallet`、`pay_wallet_transaction`、`pay_wallet_recharge_package`
- **分账管理**：`pay_order_alloc`、`pay_order_alloc_detail`

### 08-job-scheduler.sql - 定时任务模块
- **Quartz表**：`QRTZ_JOB_DETAILS`、`QRTZ_TRIGGERS`、`QRTZ_SIMPLE_TRIGGERS`、`QRTZ_CRON_TRIGGERS`等
- **平台任务**：`infra_job`、`infra_job_log`

## 数据库统计信息

- **总表数量**：约160+个唯一表（去重后）
- **主要功能模块**：8个
- **支持的核心功能**：
  - ✅ 线下课程管理
  - ✅ 线上订单系统
  - ✅ 多机构入驻
  - ✅ 课程分类管理
  - ✅ 积分体系
  - ✅ 二级分销
  - ✅ 会员等级
  - ✅ 拼团活动
  - ✅ 99元试听卡
  - ✅ 营销工具
  - ✅ 地址距离计算

## 核心功能特色

### 1. 99元试听卡功能
- 可选择3个机构，每个机构试听2节课
- 支持灵活的使用记录和状态管理
- 表：`education_user_trial_card`、`education_trial_card_usage`

### 2. 多机构管理
- 机构入驻申请审核流程
- 机构分类和评级管理
- 支持位置信息和距离计算
- 表：`education_institution`、`education_institution_apply`

### 3. 分销体系
- 二级分佣结构
- 推广员管理和佣金提现
- 完整的分销记录追踪
- 表：`brokerage_user`、`brokerage_record`、`brokerage_withdraw`

### 4. 课程与班级管理
- 课程分类层级管理
- 班级容量和学员管理
- 教师与课程关联
- 学习记录和考勤管理

## 使用说明

1. **数据库初始化**：按顺序执行SQL文件
2. **功能模块**：每个SQL文件独立，可根据需要选择性执行
3. **数据迁移**：保持原有字段结构，便于数据迁移
4. **扩展性**：模块化设计，便于后续功能扩展

## 注意事项

1. 所有表都保留了原有的字段属性，只修改了注释
2. 新增的education相关表适配少儿教育平台特点
3. 建议在正式环境使用前进行充分测试
4. 可选择性地移除jimureport和重复的quartz表以优化数据库

## 技术特点

- **多租户支持**：所有核心表支持tenant_id
- **软删除设计**：统一使用deleted字段
- **审计字段**：标准的creator、create_time、updater、update_time字段
- **索引优化**：关键字段建立了合适的索引
- **数据完整性**：通过外键和约束保证数据一致性

此次重新整理后的数据库结构完全支持少儿教育平台的所有需求功能，为平台的稳定运行提供了坚实的数据基础。 