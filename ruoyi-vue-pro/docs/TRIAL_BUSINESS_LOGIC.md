# 试听权益业务逻辑设计

## 核心原则

**试听权益不叠加，按最高等级享受权益**

## 业务规则

### 1. 会员等级计算
- 按用户**累计充值金额**确定会员等级
- 充值金额越高，会员等级越高
- 等级一旦达到，不会降级

### 2. 试听权益规则
- 用户只享受**当前最高等级**对应的试听权益
- 升级时，试听权益**重置**为新等级的完整权益
- 试听权益有固定有效期

### 3. 充值与升级逻辑

#### 等级配置示例
| 等级 | 名称 | 最低充值金额 | 试听机构数 | 有效期 |
|------|------|-------------|------------|--------|
| 1 | 铜牌会员 | 99元 | 3个机构 | 365天 |
| 2 | 银牌会员 | 199元 | 5个机构 | 365天 |
| 3 | 金牌会员 | 399元 | 8个机构 | 365天 |

#### 业务场景

**场景1：首次充值**
```
用户充值99元
→ 累计充值: 99元
→ 会员等级: 铜牌会员
→ 试听权益: 3个机构，365天有效期
→ 可用机构数: 3个
```

**场景2：同等级重复充值**
```
铜牌会员再充值99元
→ 累计充值: 198元
→ 会员等级: 银牌会员（升级）
→ 试听权益: 重置为5个机构，365天有效期
→ 可用机构数: 5个（重置，之前使用记录清零）
```

**场景3：跨等级充值**
```
铜牌会员直接充值300元
→ 累计充值: 399元
→ 会员等级: 金牌会员（跨级升级）
→ 试听权益: 重置为8个机构，365天有效期
→ 可用机构数: 8个（重置）
```

## 数据结构设计

### member_level（等级配置）
```sql
{
  "id": 1,
  "name": "铜牌会员",
  "level": 1,
  "min_recharge_amount": 9900,  -- 最低充值金额(分)
  "trial_institution_count": 3, -- 可试听机构数
  "trial_validity_days": 365    -- 有效期天数
}
```

### member_user（用户状态）
```sql
{
  "id": 1001,
  "level_id": 1,                    -- 当前等级
  "total_recharge_amount": 9900,    -- 累计充值金额
  "trial_used_institutions": 1,     -- 已使用机构数
  "trial_expire_time": "2025-12-31" -- 权益过期时间
}
```

### member_recharge_record（充值记录）
```sql
{
  "id": 1,
  "user_id": 1001,
  "recharge_amount": 9900,     -- 本次充值金额
  "before_level_id": null,     -- 充值前等级
  "after_level_id": 1,         -- 充值后等级
  "trial_benefit_reset": true  -- 是否重置试听权益
}
```

## 核心算法

### 1. 等级计算算法
```java
public MemberLevel calculateMemberLevel(int totalRechargeAmount) {
    return memberLevelMapper.selectOne(
        Wrappers.<MemberLevel>lambdaQuery()
            .le(MemberLevel::getMinRechargeAmount, totalRechargeAmount)
            .orderByDesc(MemberLevel::getLevel)
            .last("LIMIT 1")
    );
}
```

### 2. 试听权益重置算法
```java
public void resetTrialBenefit(Long userId, MemberLevel newLevel) {
    MemberUser user = memberUserMapper.selectById(userId);
    
    // 重置试听权益
    user.setTrialUsedInstitutions(0);
    user.setTrialExpireTime(LocalDateTime.now().plusDays(newLevel.getTrialValidityDays()));
    
    memberUserMapper.updateById(user);
}
```

### 3. 试听权益检查算法
```java
public boolean checkTrialEligibility(Long userId, Long institutionId) {
    MemberUser user = memberUserMapper.selectById(userId);
    MemberLevel level = memberLevelMapper.selectById(user.getLevelId());
    
    // 检查权益是否过期
    if (user.getTrialExpireTime().isBefore(LocalDateTime.now())) {
        return false;
    }
    
    // 检查机构数量限制
    if (user.getTrialUsedInstitutions() >= level.getTrialInstitutionCount()) {
        return false;
    }
    
    // 检查是否已在该机构试听过
    return !educationTrialUsageMapper.existsByUserIdAndInstitutionId(userId, institutionId);
}
```

## 业务优势

### 1. 逻辑清晰
- 一个用户同时只有一种试听权益
- 避免多重权益叠加的复杂性
- 等级与权益直接对应

### 2. 激励升级
- 用户倾向于一次性充值更高金额
- 升级时获得完整的新权益
- 提高用户粘性和ARPU值

### 3. 运营友好
- 权益规则易于理解和解释
- 客服处理投诉简单
- 数据统计和分析清晰

### 4. 技术实现简单
- 状态机制清晰
- 数据一致性容易保证
- 避免复杂的权益计算逻辑

## 注意事项

### 1. 升级确认
升级时可以给用户发送通知：
```
恭喜您升级为银牌会员！
您的试听权益已更新：
- 可试听机构：5个
- 有效期：365天
- 立即生效
```

### 2. 权益过期提醒
在权益即将过期时提醒用户：
```
您的试听权益将于3天后过期
已使用：2/3个机构
建议尽快使用剩余权益
```

### 3. 数据迁移
对于已有用户，需要根据历史充值记录计算当前等级和权益。 