-- =============================================
-- 会员套餐配置菜单权限 SQL
-- 创建时间：2025-06-17
-- 说明：为会员套餐配置功能添加菜单和权限
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 添加会员套餐配置菜单
-- ----------------------------

-- 1. 添加会员套餐配置主菜单
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(2720, '会员套餐配置', '', 2, 5, 2262, 'package-config', 'fa:package', 'member/packageConfig/index', 'MemberPackageConfig', 0, b'1', b'1', b'1', '1', '2024-01-01 00:00:00', '1', '2024-01-01 00:00:00', b'0');

-- 2. 添加会员套餐配置权限按钮
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(2721, '套餐配置查询', 'member:package-config:query', 3, 1, 2720, '', '', '', '', 0, b'1', b'1', b'1', '1', '2024-01-01 00:00:00', '1', '2024-01-01 00:00:00', b'0'),
(2722, '套餐配置创建', 'member:package-config:create', 3, 2, 2720, '', '', '', '', 0, b'1', b'1', b'1', '1', '2024-01-01 00:00:00', '1', '2024-01-01 00:00:00', b'0'),
(2723, '套餐配置更新', 'member:package-config:update', 3, 3, 2720, '', '', '', '', 0, b'1', b'1', b'1', '1', '2024-01-01 00:00:00', '1', '2024-01-01 00:00:00', b'0'),
(2724, '套餐配置删除', 'member:package-config:delete', 3, 4, 2720, '', '', '', '', 0, b'1', b'1', b'1', '1', '2024-01-01 00:00:00', '1', '2024-01-01 00:00:00', b'0');

-- ----------------------------
-- 为超级管理员分配权限
-- ----------------------------

-- 为超级管理员角色(role_id=1)分配会员套餐配置权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
(1, 2720, '1', NOW(), '1', NOW(), 1),
(1, 2721, '1', NOW(), '1', NOW(), 1),
(1, 2722, '1', NOW(), '1', NOW(), 1),
(1, 2723, '1', NOW(), '1', NOW(), 1),
(1, 2724, '1', NOW(), '1', NOW(), 1);

-- ----------------------------
-- 启用菜单状态
-- ----------------------------

-- 启用所有会员套餐配置相关菜单
UPDATE `system_menu` SET `status` = 0 WHERE `id` IN (2720, 2721, 2722, 2723, 2724);

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 执行完成后说明
-- =============================================

/*
执行此SQL后，会员套餐配置功能将具备以下权限：
1. 会员套餐配置主菜单 (id: 2720)
2. 查询权限 (member:package-config:query)
3. 创建权限 (member:package-config:create)  
4. 更新权限 (member:package-config:update)
5. 删除权限 (member:package-config:delete)

超级管理员将自动获得所有权限。
其他角色需要在系统管理-角色管理中手动分配权限。
*/
