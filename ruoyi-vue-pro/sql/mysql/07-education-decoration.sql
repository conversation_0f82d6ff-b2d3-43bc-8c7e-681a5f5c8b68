-- =============================================
-- 教育平台装修配置 SQL
-- 基于图片内容配置的教育主题装修
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 删除旧数据并插入新的教育装修模板
-- ----------------------------

-- 删除现有装修模板（保留原系统其他数据）
DELETE FROM `promotion_diy_template` WHERE `name` LIKE '%教育%' OR `name` LIKE '%趣学%';

-- 删除现有装修页面
DELETE FROM `promotion_diy_page` WHERE `name` LIKE '%教育%' OR `name` LIKE '%趣学%';

-- 插入教育主题装修模板
INSERT INTO `promotion_diy_template` (`id`, `name`, `used`, `used_time`, `remark`, `preview_pic_urls`, `property`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES 
(20, '趣学教育主模板', b'1', NOW(), '儿童教育平台装修模板，适合试听券团购场景', 
'https://img.alicdn.com/imgextra/i1/O1CN01kKzMqP1yGLbOsNQbO_!!6000000006562-2-tps-750-1334.png', 
JSON_OBJECT(
  'page', JSON_OBJECT(
    'description', '趣学教育 - 99元3门课程全城任选',
    'backgroundColor', '#f8f9fa',
    'backgroundImage', ''
  ),
  'navigationBar', JSON_OBJECT(
    'bgType', 'color',
    'bgColor', '#ffffff',
    'bgImg', '',
    'styleType', 'normal',
    'alwaysShow', true,
    'mpCells', JSON_ARRAY(JSON_OBJECT('type', 'text', 'textColor', '#333333')),
    'otherCells', JSON_ARRAY(JSON_OBJECT('type', 'text', 'textColor', '#333333'))
  ),
  'tabBar', JSON_OBJECT(
    'theme', 'orange',
    'style', JSON_OBJECT(
      'bgType', 'color',
      'bgColor', '#ffffff',
      'color', '#999999',
      'activeColor', '#ff6b35'
    ),
    'items', JSON_ARRAY(
      JSON_OBJECT(
        'text', '课程',
        'url', '/pages/index/index',
        'iconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjOTk5OTk5Ii8+PC9zdmc+',
        'activeIconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjZmY2YjM1Ii8+PC9zdmc+'
      ),
      JSON_OBJECT(
        'text', '机构',
        'url', '/pages/index/category',
        'iconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjciIGhlaWdodD0iNyIgZmlsbD0iIzk5OTk5OSIvPjxyZWN0IHg9IjE0IiB5PSIzIiB3aWR0aD0iNyIgaGVpZ2h0PSI3IiBmaWxsPSIjOTk5OTk5Ii8+PHJlY3QgeD0iMyIgeT0iMTQiIHdpZHRoPSI3IiBoZWlnaHQ9IjciIGZpbGw9IiM5OTk5OTkiLz48cmVjdCB4PSIxNCIgeT0iMTQiIHdpZHRoPSI3IiBoZWlnaHQ9IjciIGZpbGw9IiM5OTk5OTkiLz48L3N2Zz4=',
        'activeIconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjciIGhlaWdodD0iNyIgZmlsbD0iI2ZmNmIzNSIvPjxyZWN0IHg9IjE0IiB5PSIzIiB3aWR0aD0iNyIgaGVpZ2h0PSI3IiBmaWxsPSIjZmY2YjM1Ii8+PHJlY3QgeD0iMyIgeT0iMTQiIHdpZHRoPSI3IiBoZWlnaHQ9IjciIGZpbGw9IiNmZjZiMzUiLz48cmVjdCB4PSIxNCIgeT0iMTQiIHdpZHRoPSI3IiBoZWlnaHQ9IjciIGZpbGw9IiNmZjZiMzUiLz48L3N2Zz4='
      ),
      JSON_OBJECT(
        'text', '我的',
        'url', '/pages/index/user',
        'iconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTIiIGN5PSI4IiByPSIzIiBmaWxsPSIjOTk5OTk5Ii8+PHBhdGggZD0iTTEyIDE0QzE2LjQgMTQgMjAgMTUuOCAyMCAxOFYyMEg0VjE4QzQgMTUuOCA3LjYgMTQgMTIgMTRaIiBmaWxsPSIjOTk5OTk5Ii8+PC9zdmc+',
        'activeIconUrl', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTIiIGN5PSI4IiByPSIzIiBmaWxsPSIjZmY2YjM1Ii8+PHBhdGggZD0iTTEyIDE0QzE2LjQgMTQgMjAgMTUuOCAyMCAxOFYyMEg0VjE4QzQgMTUuOCA3LjYgMTQgMTIgMTRaIiBmaWxsPSIjZmY2YjM1Ii8+PC9zdmc+'
      )
    )
  ),
  'components', JSON_ARRAY()
), '1', NOW(), '1', NOW(), b'0', 1);

-- 插入首页装修配置
INSERT INTO `promotion_diy_page` (`id`, `template_id`, `name`, `remark`, `preview_pic_urls`, `property`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES 
(20, 20, '首页', '教育平台首页', 
'https://img.alicdn.com/imgextra/i1/O1CN01kKzMqP1yGLbOsNQbO_!!6000000006562-2-tps-750-1334.png',
JSON_OBJECT(
  'components', JSON_ARRAY(
    -- 搜索框组件
    JSON_OBJECT(
      'id', 'SearchBar',
      'property', JSON_OBJECT(
        'placeholder', '感兴趣的课程',
        'buttonText', '搜索',
        'bgColor', '#ff6b35',
        'textColor', '#ffffff',
        'style', JSON_OBJECT(
          'margin', 10,
          'borderRadius', 25
        )
      )
    ),
    
    -- 会员开通横幅
    JSON_OBJECT(
      'id', 'ImageBar',
      'property', JSON_OBJECT(
        'imageUrl', 'https://img.alicdn.com/imgextra/i4/O1CN01qzQODq1f8VrkNyeM9_!!6000000003965-2-tps-750-200.png',
        'title', '99元 3门课程・全城任选',
        'subtitle', '开通会员',
        'linkType', 'page',
        'linkUrl', '/pages/member/index',
        'bgColor', '#4a5568',
        'textColor', '#ffffff',
        'style', JSON_OBJECT(
          'margin', 10,
          'borderRadius', 12,
          'backgroundType', 'gradient',
          'background', 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)'
        )
      )
    ),
    
    -- 课程分类宫格
    JSON_OBJECT(
      'id', 'MenuGrid',
      'property', JSON_OBJECT(
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'columns', 4,
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '舞蹈',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#ff6b35',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=dance'
          ),
          JSON_OBJECT(
            'text', '编程',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#4299e1',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=coding'
          ),
          JSON_OBJECT(
            'text', '数学',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#f6ad55',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=math'
          ),
          JSON_OBJECT(
            'text', '书法',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#9f7aea',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=calligraphy'
          ),
          JSON_OBJECT(
            'text', '绘画',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#48bb78',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=art'
          ),
          JSON_OBJECT(
            'text', '乐器',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#ed8936',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=music'
          ),
          JSON_OBJECT(
            'text', '口才',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#38b2ac',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=speech'
          ),
          JSON_OBJECT(
            'text', '体能',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#e53e3e',
            'linkType', 'page',
            'linkUrl', '/pages/course/category?type=sports'
          )
        )
      )
    ),
    
    -- 排序筛选
    JSON_OBJECT(
      'id', 'MenuList',
      'property', JSON_OBJECT(
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 10,
          'bgColor', '#ffffff',
          'borderRadius', 8
        ),
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '综合排序',
            'active', true,
            'linkType', 'action',
            'action', 'sort',
            'value', 'default'
          ),
          JSON_OBJECT(
            'text', '低价优先',
            'active', false,
            'linkType', 'action',
            'action', 'sort',
            'value', 'price_low'
          )
        )
      )
    ),
    
    -- 推荐课程列表
    JSON_OBJECT(
      'id', 'ProductList',
      'property', JSON_OBJECT(
        'title', '推荐课程',
        'more', JSON_OBJECT(
          'show', true,
          'text', '查看更多',
          'linkType', 'page',
          'linkUrl', '/pages/course/list'
        ),
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'layoutType', 'list',
        'columnCount', 1,
        'goodsFields', JSON_ARRAY('image', 'title', 'price', 'originalPrice', 'salesCount'),
        'showButton', true,
        'buttonText', '特惠报名'
      )
    )
  )
), '1', NOW(), '1', NOW(), b'0', 1);

-- 插入用户页面装修配置
INSERT INTO `promotion_diy_page` (`id`, `template_id`, `name`, `remark`, `preview_pic_urls`, `property`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES 
(21, 20, '我的', '教育平台用户中心页面', 
'https://img.alicdn.com/imgextra/i2/O1CN01wGhDhP1jZtBXVa5Oc_!!6000000004563-2-tps-750-1334.png',
JSON_OBJECT(
  'components', JSON_ARRAY(
    -- 用户信息卡片
    JSON_OBJECT(
      'id', 'UserCard',
      'property', JSON_OBJECT(
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 20,
          'bgColor', '#ffffff',
          'borderRadius', 12,
          'backgroundType', 'gradient',
          'background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        ),
        'showLevel', true,
        'showMoney', true,
        'showPoint', true
      )
    ),
    
    -- 会员套餐组件
    JSON_OBJECT(
      'id', 'MemberPackage',
      'property', JSON_OBJECT(
        'title', '会员套餐',
        'subtitle', '开通会员享受更多权益',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'layout', 'card',
        'showDiscount', true,
        'showBenefits', true
      )
    ),
    
    -- 我的服务宫格
    JSON_OBJECT(
      'id', 'MenuGrid',
      'property', JSON_OBJECT(
        'title', '我的服务',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'columns', 4,
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '试听券',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#ff6b35',
            'linkType', 'page',
            'linkUrl', '/pages/voucher/index'
          ),
          JSON_OBJECT(
            'text', '我的课程',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#4299e1',
            'linkType', 'page',
            'linkUrl', '/pages/course/my'
          ),
          JSON_OBJECT(
            'text', '学习记录',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#48bb78',
            'linkType', 'page',
            'linkUrl', '/pages/study/record'
          ),
          JSON_OBJECT(
            'text', '收藏课程',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#ed8936',
            'linkType', 'page',
            'linkUrl', '/pages/course/favorite'
          )
        )
      )
    ),
    
    -- 我的订单
    JSON_OBJECT(
      'id', 'UserOrder',
      'property', JSON_OBJECT(
        'title', '我的订单',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'showAll', true,
        'showStatusCount', true
      )
    ),
    
    -- 推广分享 (基于分销功能)
    JSON_OBJECT(
      'id', 'MenuGrid',
      'property', JSON_OBJECT(
        'title', '推广分享',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'columns', 2,
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '邀请好友',
            'subtitle', '赚取佣金',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#ff6b35',
            'linkType', 'page',
            'linkUrl', '/pages/commission/index'
          ),
          JSON_OBJECT(
            'text', '分销中心',
            'subtitle', '查看收益',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'bgColor', '#38b2ac',
            'linkType', 'page',
            'linkUrl', '/pages/commission/center'
          )
        )
      )
    ),
    
    -- 其他功能
    JSON_OBJECT(
      'id', 'MenuList',
      'property', JSON_OBJECT(
        'title', '其他功能',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12
        ),
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '客服咨询',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'linkType', 'page',
            'linkUrl', '/pages/chat/index'
          ),
          JSON_OBJECT(
            'text', '意见反馈',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'linkType', 'page',
            'linkUrl', '/pages/feedback/index'
          ),
          JSON_OBJECT(
            'text', '关于我们',
            'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
            'linkType', 'page',
            'linkUrl', '/pages/about/index'
          )
        )
      )
    )
  )
), '1', NOW(), '1', NOW(), b'0', 1);

-- 插入机构页面装修配置
INSERT INTO `promotion_diy_page` (`id`, `template_id`, `name`, `remark`, `preview_pic_urls`, `property`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES 
(22, 20, '机构', '教育机构列表页面', 
'https://img.alicdn.com/imgextra/i3/O1CN01MGZhJu1vZnP8KKvVb_!!6000000006189-2-tps-750-1334.png',
JSON_OBJECT(
  'components', JSON_ARRAY(
    -- 位置定位组件
    JSON_OBJECT(
      'id', 'TitleBar',
      'property', JSON_OBJECT(
        'title', '北京市海淀区XXX路XXX号XX',
        'iconUrl', 'https://img.alicdn.com/tfs/TB1oYngayrpK1RjSZFhXXXSdXXa-144-144.png',
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 15,
          'bgColor', '#ffffff',
          'borderRadius', 12,
          'textAlign', 'left'
        )
      )
    ),
    
    -- 机构筛选
    JSON_OBJECT(
      'id', 'MenuList',
      'property', JSON_OBJECT(
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 10,
          'bgColor', '#ffffff',
          'borderRadius', 8
        ),
        'list', JSON_ARRAY(
          JSON_OBJECT(
            'text', '综合排序',
            'active', true,
            'linkType', 'action',
            'action', 'sort',
            'value', 'default'
          ),
          JSON_OBJECT(
            'text', '低价优先',
            'active', false,
            'linkType', 'action',
            'action', 'sort',
            'value', 'price_low'
          )
        )
      )
    ),
    
    -- 机构列表
    JSON_OBJECT(
      'id', 'ProductList',
      'property', JSON_OBJECT(
        'style', JSON_OBJECT(
          'margin', 10,
          'padding', 0,
          'bgColor', 'transparent'
        ),
        'layoutType', 'list',
        'columnCount', 1,
        'goodsFields', JSON_ARRAY('image', 'title', 'subtitle', 'price', 'distance', 'rating'),
        'showButton', true,
        'buttonText', '特惠报名'
      )
    )
  )
), '1', NOW(), '1', NOW(), b'0', 1);

-- 更新默认使用的模板
UPDATE `promotion_diy_template` SET `used` = b'0' WHERE `used` = b'1';
UPDATE `promotion_diy_template` SET `used` = b'1', `used_time` = NOW() WHERE `id` = 20;

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 装修配置完成
-- 现在您的小程序将显示教育主题的装修效果
-- =============================================