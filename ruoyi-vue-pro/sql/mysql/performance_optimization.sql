-- 性能优化SQL脚本
-- 为经常查询的字段添加索引

-- 1. 字典数据表优化
-- 为status字段添加索引，因为经常按状态查询
CREATE INDEX IF NOT EXISTS idx_system_dict_data_status ON system_dict_data(status);
-- 为dict_type字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_dict_data_type ON system_dict_data(dict_type);
-- 复合索引：status + dict_type
CREATE INDEX IF NOT EXISTS idx_system_dict_data_status_type ON system_dict_data(status, dict_type);

-- 2. 菜单表优化
-- 为status字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_menu_status ON system_menu(status);
-- 为parent_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_menu_parent_id ON system_menu(parent_id);
-- 复合索引：status + parent_id
CREATE INDEX IF NOT EXISTS idx_system_menu_status_parent ON system_menu(status, parent_id);

-- 3. 用户角色关联表优化
-- 为user_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_user_role_user_id ON system_user_role(user_id);
-- 为role_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_user_role_role_id ON system_user_role(role_id);
-- 为tenant_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_user_role_tenant_id ON system_user_role(tenant_id);

-- 4. 角色表优化
-- 为status字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_role_status ON system_role(status);
-- 为tenant_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_role_tenant_id ON system_role(tenant_id);

-- 5. 用户表优化
-- 为status字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_users_status ON system_users(status);
-- 为tenant_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_users_tenant_id ON system_users(tenant_id);

-- 6. OAuth2访问令牌表优化
-- 为access_token字段添加索引
CREATE INDEX IF NOT EXISTS idx_oauth2_access_token_token ON system_oauth2_access_token(access_token);
-- 为refresh_token字段添加索引
CREATE INDEX IF NOT EXISTS idx_oauth2_access_token_refresh ON system_oauth2_access_token(refresh_token);
-- 为user_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_oauth2_access_token_user_id ON system_oauth2_access_token(user_id);
-- 为expires_time字段添加索引
CREATE INDEX IF NOT EXISTS idx_oauth2_access_token_expires ON system_oauth2_access_token(expires_time);

-- 7. 租户表优化
-- 为status字段添加索引
CREATE INDEX IF NOT EXISTS idx_system_tenant_status ON system_tenant(status);

-- 8. 商品相关表优化（如果存在）
-- 商品SPU表
CREATE INDEX IF NOT EXISTS idx_product_spu_status ON product_spu(status) WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_spu');

-- 9. 促销活动相关表优化
-- 优惠券模板表
CREATE INDEX IF NOT EXISTS idx_promotion_coupon_template_status ON promotion_coupon_template(status) WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'promotion_coupon_template');

-- 10. 会员用户表优化
-- 为status字段添加索引
CREATE INDEX IF NOT EXISTS idx_member_user_status ON member_user(status) WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'member_user');
-- 为tenant_id字段添加索引
CREATE INDEX IF NOT EXISTS idx_member_user_tenant_id ON member_user(tenant_id) WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'member_user');

-- 查看索引创建情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM 
    information_schema.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE 'idx_%'
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX; 