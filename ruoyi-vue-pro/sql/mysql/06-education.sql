-- =============================================
-- 教育团购平台扩展 - 最小化修改版本
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 扩展字段：商品SPU表 - 增加课程相关字段
-- ----------------------------
ALTER TABLE `product_spu` 
ADD COLUMN `class_count` int NOT NULL DEFAULT 2 COMMENT '上课次数（即试听课时数）' AFTER `give_integral`,
ADD COLUMN `minutes_per_class` int NOT NULL DEFAULT 45 COMMENT '每课时分钟数' AFTER `class_count`,
ADD COLUMN `hours_per_class` int NOT NULL DEFAULT 2 COMMENT '每次课时（1课时/2课时/3课时/4课时）' AFTER `minutes_per_class`,
ADD COLUMN `age_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '3-12岁' COMMENT '适合年龄段（如：3-6岁）' AFTER `hours_per_class`,
ADD COLUMN `course_target` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课程目标' AFTER `age_range`,
ADD COLUMN `is_membership_package` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否会员套餐' AFTER `course_target`;

-- 删除总课时字段和试听课时字段（如果存在的话）
ALTER TABLE `product_spu` DROP COLUMN IF EXISTS `total_lessons`;
ALTER TABLE `product_spu` DROP COLUMN IF EXISTS `trial_lessons`;

-- ----------------------------
-- 扩展字段：商品SKU表 - 增加自提门店trade_delivery_pick_up_store关联
-- ----------------------------
ALTER TABLE `product_sku` 
ADD COLUMN `pick_up_store_id` bigint NULL DEFAULT NULL COMMENT '取货门店ID' AFTER `bar_code`,
ADD INDEX `idx_pick_up_store_id`(`pick_up_store_id` ASC) USING BTREE;


-- ----------------------------
-- 扩展字段：优惠券模板表 - 增加试听券标识
-- ----------------------------
ALTER TABLE `promotion_coupon_template` 
ADD COLUMN `is_trial_voucher` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否试听券' AFTER `fixed_end_term`;

-- ----------------------------
-- 扩展字段：优惠券表 - 增加试听券标识
-- ----------------------------
ALTER TABLE `promotion_coupon` 
ADD COLUMN `is_trial_voucher` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否试听券' AFTER `use_time`;

-- ----------------------------
-- 扩展字段：会员用户表 - 增加地理位置字段（可选）
-- ----------------------------
ALTER TABLE `member_user` 
ADD COLUMN `current_latitude` decimal(10,7) NULL DEFAULT NULL COMMENT '当前纬度' AFTER `group_id`,
ADD COLUMN `current_longitude` decimal(10,7) NULL DEFAULT NULL COMMENT '当前经度' AFTER `current_latitude`,
ADD COLUMN `current_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '当前地址' AFTER `current_longitude`;

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 数据字典建议配置
-- =============================================

/*
建议在系统字典中添加以下配置：

1. 课程年龄段枚举 (course_age_range)
   - 0-3岁
   - 3-6岁  
   - 6-12岁
   - 3-12岁
   - 12-18岁
   - 成人

2. 每次课时枚举 (hours_per_class)
   - 1: 1课时
   - 2: 2课时
   - 3: 3课时
   - 4: 4课时

3. 试听券类型枚举 (trial_voucher_type)
   - 0: 普通优惠券
   - 1: 试听券

4. 业务流程说明：
   - 用户购买99元会员 → 系统自动发放3张试听券
   - 用户购买128元会员 → 系统自动发放5张试听券
   - 试听券可抵扣任何价格的课程，前端不显示原价
   - 上课次数即为试听课时数
   - 所有交易记录在现有订单表中，无需额外试听记录表
*/

-- 创建会员套餐配置表
CREATE TABLE `member_package_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) NOT NULL COMMENT '套餐名称',
  `price` int NOT NULL COMMENT '套餐价格（分）',
  `voucher_count` int NOT NULL COMMENT '赠送试听券数量',
  `voucher_template_id` bigint NOT NULL COMMENT '试听券模板ID',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员套餐配置表';

-- 插入默认会员套餐配置数据
INSERT INTO `member_package_config` (`id`, `name`, `price`, `voucher_count`, `voucher_template_id`, `sort`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '基础套餐', 9900, 3, 1, 1, 1, '99元可兑换3个机构试听课程', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '进阶套餐', 12800, 5, 1, 2, 1, '128元可兑换5个机构试听课程', 'admin', NOW(), 'admin', NOW(), b'0');
